import React, { useState } from "react";
import { X, Upload, <PERSON>ert<PERSON>riangle, Info, Check, FileText, HelpCircle, Microscope } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import WorkflowStepIndicator from "./WorkflowStepIndicator";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

import { Calendar } from "@/components/ui/calendar";
import { TimePicker } from "@/components/ui/time-picker";

import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import PhotoUpload from "./PhotoUpload";
import InvestigationAnalysisForm from "./InvestigationAnalysisForm";
import ComprehensiveInvestigationForm from "./ComprehensiveInvestigationForm";
import { cities, businessUnits, projectOptions } from "@/utils/formData";
import { useUser } from "@/contexts/UserContext";

// Removed unused select function

// Helper function to get incident type label
const getIncidentTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    "fall": "Fall",
    "slip": "Slip",
    "nearMiss": "Near Miss",
    "injury": "Injury",
    "environmental": "Environmental",
    "health": "Health",
    "safety": "Safety",
    "propertyDamage": "Property Damage",
    "security": "Security",
    "quality": "Quality",
    "fire": "Fire",
    "electrical": "Electrical",
    "chemical": "Chemical",
    "vehicle": "Vehicle",
    "other": "Other"
  };

  return typeMap[type] || type;
};

// Helper function to get incident category label
const getIncidentCategoryLabel = (category: string): string => {
  const categoryMap: Record<string, string> = {
    "safety": "Safety",
    "environmental": "Environmental",
    "security": "Security",
    "quality": "Quality",
    "health": "Health",
    "fire": "Fire",
    "electrical": "Electrical",
    "chemical": "Chemical",
    "vehicle": "Vehicle",
    "machinery": "Machinery",
    "other": "Other"
  };

  return categoryMap[category] || category;
};

// Helper function to get surface type label
const getSurfaceTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    "concrete": "Concrete",
    "carpet": "Carpet",
    "tile": "Tile",
    "wood": "Wood",
    "metal": "Metal",
    "asphalt": "Asphalt",
    "grass": "Grass",
    "other": "Other"
  };

  return typeMap[type] || type;
};

// Helper function to get surface condition label
const getSurfaceConditionLabel = (condition: string): string => {
  const conditionMap: Record<string, string> = {
    "dry": "Dry",
    "wet": "Wet",
    "icy": "Icy",
    "oily": "Oily/Slippery",
    "damaged": "Damaged",
    "uneven": "Uneven",
    "other": "Other"
  };

  return conditionMap[condition] || condition;
};

// Helper function to get lighting label
const getLightingLabel = (lighting: string): string => {
  const lightingMap: Record<string, string> = {
    "good": "Good",
    "adequate": "Adequate",
    "poor": "Poor",
    "none": "None"
  };

  return lightingMap[lighting] || lighting;
};

// Helper function to get weather condition label
const getWeatherConditionLabel = (condition: string): string => {
  const conditionMap: Record<string, string> = {
    "clear": "Clear",
    "cloudy": "Cloudy",
    "rain": "Rain",
    "snow": "Snow",
    "fog": "Fog",
    "wind": "Windy",
    "storm": "Storm",
    "indoor": "Indoor (N/A)"
  };

  return conditionMap[condition] || condition;
};

// Helper function to get legal classification label
const getLegalClassificationLabel = (classification: string): string => {
  const classificationMap: Record<string, string> = {
    "Normal": "Normal",
    "Client Privilege": "Client Privilege",
  };

  return classificationMap[classification] || classification;
};

// Define the validation schema for the action form
const actionFormSchema = z.object({
  // Initial incident details (editable)
  incidentTitle: z.string().min(1, "Incident title is required"),
  incidentDate: z.date(),
  incidentTime: z.string(),
  incidentType: z.string(),
  incidentCategory: z.string(),
  description: z.string().min(1, "Description is required"),
  location: z.object({
    country: z.string(),
    city: z.string().optional(),
    businessUnit: z.string(),
    projectDcOps: z.string().optional(),
    levelAndLocation: z.string().optional(),
  }),

  // Injury Classification
  injuryClassification: z.object({
    isWorkRelated: z.boolean().nullable().default(null),
    lossOfConsciousness: z.boolean().nullable().default(null),
    isDangerousOccurrence: z.boolean().nullable().default(null),
    isFatality: z.boolean().nullable().default(null),
    isPermanentDisability: z.boolean().nullable().default(null),
    isLostTimeIncident: z.boolean().nullable().default(null),
    isMedicalTreatment: z.boolean().nullable().default(null),
    isFirstAid: z.boolean().nullable().default(null),
  }),

  // Potentially Serious Incident Classification
  potentiallySerious: z.object({
    couldResultInFatality: z.boolean().default(false),
    couldResultInPermanentDisability: z.boolean().default(false),
  }),

  // Additional fields from the second image
  propertyDamage: z.boolean().default(false),
  propertyDamageDetails: z.string().optional(),

  surfaceType: z.string().optional(),
  surfaceCondition: z.string().optional(),
  lighting: z.string().optional(),
  weatherCondition: z.string().optional(),

  reportableToAuthorities: z.boolean().default(false),
  reportableDetails: z.string().optional(),

  immediateActionDate: z.date().optional(),
  immediateActionsTaken: z.string().optional(),

  legalClassification: z.string().optional(),

  photos: z.array(z.instanceof(File)).default([]),



  // Control Measures fields
  incidentGMS: z.string().optional(),
  stopWorkOrder: z.boolean().default(false),
  rootCauseAnalysis: z.string().optional(),
  controlMeasuresRequired: z.boolean().default(false),
  controlMeasuresDescription: z.string().optional(),
  controlMeasures: z.array(
    z.object({
      description: z.string(),
      dueDate: z.date().optional(),
      personResponsible: z.string().optional(),
    })
  ).default([]),
  riskAssessmentRequired: z.boolean().default(false),
  riskAssessmentDescription: z.string().optional(),
  riskAssessments: z.array(
    z.object({
      name: z.string(),
      dueDate: z.date().optional(),
      personResponsible: z.string().optional(),
    })
  ).default([]),

  // Investigation fields
  investigationDetails: z.string().optional(),
  detailedInvestigation: z.any().optional(),
  comprehensiveInvestigation: z.any().optional(), // For the nested investigation form data
});

type ActionFormValues = z.infer<typeof actionFormSchema>;

interface IncidentActionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  incident: any | null;
  onActionComplete: (updatedIncident: any) => void;
  userRole?: 'reporter' | 'reviewer' | 'owner';
}

const IncidentActionDialog: React.FC<IncidentActionDialogProps> = ({
  open,
  onOpenChange,
  incident,
  onActionComplete,
  userRole = 'reviewer',
}) => {
  // Set default tab based on workflow stage
  const getDefaultTab = () => {
    if (!incident) return "details";

    // For investigation stage, default to investigation tab
    if (incident.status === 'submitted' && incident.stage === 'Preliminary Analysis in Progress') {
      return "investigation";
    }

    // Otherwise default to details tab
    return "details";
  };

  const [activeTab, setActiveTab] = useState(getDefaultTab());
  // State for declaration checkbox in preview tab (for reviewer only)
  const [declarationChecked, setDeclarationChecked] = useState(false);
  // Get current user information at the component level
  const { userName } = useUser();

  // Determine the current workflow stage based on incident status
  const determineWorkflowStage = () => {
    if (!incident) return 'initial';

    // Check if comprehensive investigation is required (lead investigator assigned)
    if (incident.leadInvestigator && incident.status === 'submitted' && incident.stage === 'Preliminary Analysis in Progress') {
      return 'comprehensive';
    }

    // For Reporter and Reviewer roles, if incident is submitted and in Preliminary Analysis stage,
    // they should see investigation tabs to update Investigation Details
    if (incident.status === 'submitted' && incident.stage === 'Preliminary Analysis in Progress') {
      return 'investigation';
    }

    switch (incident.status) {
      case 'draft':
        return 'initial';
      case 'submitted':
        return 'review';
      case 'under-review':
        return 'review';
      case 'investigation':
        return 'investigation';
      case 'comprehensive':
        return 'comprehensive';
      case 'closed':
        return 'comprehensive';
      default:
        return 'initial';
    }
  };

  const workflowStage = determineWorkflowStage();

  // Check if the initial report stage is completed
  const isInitialReportCompleted = workflowStage !== 'initial';

  // Set default values based on the incident data
  React.useEffect(() => {
    if (incident) {
      console.log("Loading incident data in IncidentActionDialog:", incident);

      // Map location data correctly from the incident object
      const locationData = {
        country: incident.locationCountry || incident.country || "",
        city: incident.locationCity || incident.city || "",
        businessUnit: incident.locationBusinessUnit || incident.workplaceActivity || "",
        projectDcOps: incident.locationProject || incident.projectDcOps || "",
        levelAndLocation: incident.locationDetails || incident.levelAndLocation || "",
      };

      console.log("Mapped location data:", locationData);
      console.log("Actions Taken data:", {
        immediateActionDate: incident.immediateActionDate,
        immediateActionsTaken: incident.immediateActionsTaken,
        legalClassification: incident.legalClassification
      });

      // Ensure we retain all data initiated by the Reporter
      form.reset({
        // Initial incident details
        incidentTitle: incident.incidentTitle || incident.description || "",
        incidentDate: incident.incidentDate ? new Date(incident.incidentDate) : new Date(),
        incidentTime: incident.incidentTime || format(new Date(), "HH:mm"),
        incidentType: incident.incidentType || "",
        incidentCategory: incident.incidentCategory || "",
        description: incident.description || "",
        location: locationData,
        // Injury Classification
        injuryClassification: {
          isWorkRelated: incident.isWorkRelated !== undefined ? incident.isWorkRelated : null,
          lossOfConsciousness: incident.lossOfConsciousness !== undefined ? incident.lossOfConsciousness : null,
          isDangerousOccurrence: incident.isDangerousOccurrence !== undefined ? incident.isDangerousOccurrence : null,
          isFatality: incident.injuryClassification?.isFatality !== undefined ? incident.injuryClassification.isFatality : null,
          isPermanentDisability: incident.injuryClassification?.isPermanentDisability !== undefined ? incident.injuryClassification.isPermanentDisability : null,
          isLostTimeIncident: incident.injuryClassification?.isLostTimeIncident !== undefined ? incident.injuryClassification.isLostTimeIncident : null,
          isMedicalTreatment: incident.injuryClassification?.isMedicalTreatment !== undefined ? incident.injuryClassification.isMedicalTreatment : null,
          isFirstAid: incident.injuryClassification?.isFirstAid !== undefined ? incident.injuryClassification.isFirstAid : null,
        },
        // Classification and other fields
        potentiallySerious: {
          couldResultInFatality: incident.potentiallySerious?.couldResultInFatality || false,
          couldResultInPermanentDisability: incident.potentiallySerious?.couldResultInPermanentDisability || false,
        },
        propertyDamage: incident.propertyDamage !== undefined ? incident.propertyDamage : false,
        propertyDamageDetails: incident.propertyDamageDetails || "",
        surfaceType: incident.surfaceType || "",
        surfaceCondition: incident.surfaceCondition || "",
        lighting: incident.lighting || "",
        weatherCondition: incident.weatherCondition || "",
        reportableToAuthorities: incident.reportableToAuthorities !== undefined ? incident.reportableToAuthorities : false,
        reportableDetails: incident.reportableDetails || "",
        immediateActionDate: incident.immediateActionDate ? new Date(incident.immediateActionDate) : new Date(),
        immediateActionsTaken: incident.immediateActionsTaken || "",
        legalClassification: incident.legalClassification || "",
        photos: [], // We can't convert URLs back to File objects, so we'll handle photos separately

        // Control Measures fields
        incidentGMS: incident.incidentGMS || "",
        stopWorkOrder: incident.stopWorkOrder !== undefined ? incident.stopWorkOrder : false,
        rootCauseAnalysis: incident.rootCauseAnalysis || "",
        controlMeasuresRequired: incident.controlMeasuresRequired !== undefined ? incident.controlMeasuresRequired : false,
        controlMeasuresDescription: incident.controlMeasuresDescription || "",
        controlMeasures: incident.controlMeasures || [],
        riskAssessmentRequired: incident.riskAssessmentRequired !== undefined ? incident.riskAssessmentRequired : false,
        riskAssessmentDescription: incident.riskAssessmentDescription || "",
        riskAssessments: incident.riskAssessments || [],

        // Investigation fields
        investigationDetails: incident.investigationDetails || "",
        detailedInvestigation: incident.detailedInvestigation || {},
        comprehensiveInvestigation: incident.comprehensiveInvestigation || {},
      });

      // Set the active tab based on workflow stage
      if (incident.status === 'submitted' && incident.stage === 'Preliminary Analysis in Progress') {
        // If lead investigator is assigned, show comprehensive investigation tab
        if (incident.leadInvestigator) {
          setActiveTab("comprehensive-investigation");
        } else {
          setActiveTab("investigation");
        }
      } else {
        setActiveTab("details");
      }
    }
  }, [incident]);

  const form = useForm<ActionFormValues>({
    resolver: zodResolver(actionFormSchema),
    defaultValues: {
      // Initial incident details
      incidentTitle: "",
      incidentDate: new Date(),
      incidentTime: "",
      incidentType: "",
      incidentCategory: "",
      description: "",
      location: {
        country: "",
        city: "",
        businessUnit: "",
        projectDcOps: "",
        levelAndLocation: "",
      },
      // Injury Classification
      injuryClassification: {
        isWorkRelated: null,
        lossOfConsciousness: null,
        isDangerousOccurrence: null,
        isFatality: null,
        isPermanentDisability: null,
        isLostTimeIncident: null,
        isMedicalTreatment: null,
        isFirstAid: null,
      },
      // Classification and other fields
      potentiallySerious: {
        couldResultInFatality: false,
        couldResultInPermanentDisability: false,
      },
      propertyDamage: false,
      propertyDamageDetails: "",
      surfaceType: "",
      surfaceCondition: "",
      lighting: "",
      weatherCondition: "",
      reportableToAuthorities: false,
      reportableDetails: "",
      immediateActionDate: new Date(),
      immediateActionsTaken: "",
      legalClassification: "",
      photos: [],

      // Control Measures fields
      incidentGMS: "",
      stopWorkOrder: false,
      rootCauseAnalysis: "",
      controlMeasuresRequired: false,
      controlMeasuresDescription: "",
      controlMeasures: [],
      riskAssessmentRequired: false,
      riskAssessmentDescription: "",
      riskAssessments: [],

      // Investigation fields
      investigationDetails: "",
      detailedInvestigation: {},
      comprehensiveInvestigation: {},
    },
  });

  const watchPropertyDamage = form.watch("propertyDamage");
  const watchReportableToAuthorities = form.watch("reportableToAuthorities");

  // Function to save changes without submitting or closing the dialog
  const saveChanges = () => {
    // Get current form values
    const currentValues = form.getValues();
    console.log("Saving changes:", currentValues);

    if (!incident) {
      toast.error("No incident selected");
      return;
    }

    // Create a deep copy of the incident to ensure we don't lose any data
    const updatedIncident = JSON.parse(JSON.stringify(incident));

    // Update the incident with the form data, ensuring we retain all existing data
    // Updated initial details
    updatedIncident.incidentTitle = currentValues.incidentTitle;
    updatedIncident.description = currentValues.description;
    updatedIncident.incidentDate = currentValues.incidentDate;
    updatedIncident.incidentTime = currentValues.incidentTime;
    updatedIncident.incidentType = currentValues.incidentType;
    updatedIncident.incidentCategory = currentValues.incidentCategory;

    // Map location data to both old and new field names for compatibility
    updatedIncident.locationCountry = currentValues.location.country;
    updatedIncident.locationCity = currentValues.location.city;
    updatedIncident.locationBusinessUnit = currentValues.location.businessUnit;
    updatedIncident.locationProject = currentValues.location.projectDcOps;
    updatedIncident.locationDetails = currentValues.location.levelAndLocation;

    // Keep old field names for backward compatibility
    updatedIncident.country = currentValues.location.country;
    updatedIncident.city = currentValues.location.city;
    updatedIncident.workplaceActivity = currentValues.location.businessUnit;
    updatedIncident.projectDcOps = currentValues.location.projectDcOps;
    updatedIncident.levelAndLocation = currentValues.location.levelAndLocation;

    console.log("Saving Location data:", {
      country: currentValues.location.country,
      city: currentValues.location.city,
      businessUnit: currentValues.location.businessUnit,
      projectDcOps: currentValues.location.projectDcOps,
      levelAndLocation: currentValues.location.levelAndLocation
    });

    // Injury Classification - ensure we properly merge with existing data
    updatedIncident.injuryClassification = {
      ...(updatedIncident.injuryClassification || {}),
      isFatality: currentValues.injuryClassification.isFatality,
      isPermanentDisability: currentValues.injuryClassification.isPermanentDisability,
      isLostTimeIncident: currentValues.injuryClassification.isLostTimeIncident,
      isMedicalTreatment: currentValues.injuryClassification.isMedicalTreatment,
      isFirstAid: currentValues.injuryClassification.isFirstAid,
    };

    updatedIncident.isWorkRelated = currentValues.injuryClassification.isWorkRelated;
    updatedIncident.lossOfConsciousness = currentValues.injuryClassification.lossOfConsciousness;
    updatedIncident.isDangerousOccurrence = currentValues.injuryClassification.isDangerousOccurrence;

    // Classification and additional details
    updatedIncident.potentiallySerious = {
      ...(updatedIncident.potentiallySerious || {}),
      couldResultInFatality: currentValues.potentiallySerious.couldResultInFatality,
      couldResultInPermanentDisability: currentValues.potentiallySerious.couldResultInPermanentDisability,
    };

    updatedIncident.propertyDamage = currentValues.propertyDamage;
    updatedIncident.propertyDamageDetails = currentValues.propertyDamageDetails;
    updatedIncident.surfaceType = currentValues.surfaceType;
    updatedIncident.surfaceCondition = currentValues.surfaceCondition;
    updatedIncident.lighting = currentValues.lighting;
    updatedIncident.weatherCondition = currentValues.weatherCondition;
    updatedIncident.reportableToAuthorities = currentValues.reportableToAuthorities;
    updatedIncident.reportableDetails = currentValues.reportableDetails;

    // Ensure Actions Taken details are properly saved
    updatedIncident.immediateActionDate = currentValues.immediateActionDate;
    updatedIncident.immediateActionsTaken = currentValues.immediateActionsTaken;
    updatedIncident.legalClassification = currentValues.legalClassification;

    console.log("Saving Actions Taken data:", {
      immediateActionDate: currentValues.immediateActionDate,
      immediateActionsTaken: currentValues.immediateActionsTaken,
      legalClassification: currentValues.legalClassification
    });

    // Merge photos without losing existing ones
    updatedIncident.photos = [
      ...(updatedIncident.photos || []),
      ...currentValues.photos.map(file => URL.createObjectURL(file))
    ];



    // Control Measures fields - ensure we properly merge with existing data
    updatedIncident.incidentGMS = currentValues.incidentGMS;
    updatedIncident.stopWorkOrder = currentValues.stopWorkOrder;
    updatedIncident.rootCauseAnalysis = currentValues.rootCauseAnalysis;
    updatedIncident.controlMeasuresRequired = currentValues.controlMeasuresRequired;
    updatedIncident.controlMeasuresDescription = currentValues.controlMeasuresDescription;
    updatedIncident.controlMeasures = currentValues.controlMeasures;
    updatedIncident.riskAssessmentRequired = currentValues.riskAssessmentRequired;
    updatedIncident.riskAssessmentDescription = currentValues.riskAssessmentDescription;
    updatedIncident.riskAssessments = currentValues.riskAssessments;

    // Investigation fields
    updatedIncident.investigationDetails = currentValues.investigationDetails;
    updatedIncident.detailedInvestigation = currentValues.detailedInvestigation;
    updatedIncident.comprehensiveInvestigation = currentValues.comprehensiveInvestigation;

    // Don't change status or workflow stage when just saving
    // Preserve the stage field if it exists
    updatedIncident.stage = updatedIncident.stage || (updatedIncident.status === "under-review" ? "Supplementary information in Progress" : undefined);

    // Set the reviewer name when a reviewer saves changes
    updatedIncident.reviewedBy = userRole === 'reviewer' ? userName : updatedIncident.reviewedBy;

    // Keep the requiresAction flag as true when saving
    updatedIncident.requiresAction = true;
    updatedIncident.lastSavedAt = new Date();
    updatedIncident.lastSavedBy = userName;

    // Original reporter information is already preserved in the spread operator above

    // Ensure we preserve any investigation details that might exist
    if (updatedIncident.investigationDetails) {
      // Keep existing investigation details intact
      updatedIncident.investigationDetails = {
        ...updatedIncident.investigationDetails,
        // Any specific updates to investigation details would go here
      };
    }

    // Update the incident in context
    onActionComplete(updatedIncident);

    // Show success message
    toast.success("Changes saved successfully", {
      description: "Your changes have been saved. You can continue editing or navigate to other tabs.",
    });
  };

  const onSubmit = (data: ActionFormValues) => {
    console.log("Action form submitted:", data);
    console.log("Location data being submitted:", data.location);

    if (!incident) {
      toast.error("No incident selected");
      return;
    }



    // Calculate incident classification level based on injury classification
    let incidentClassificationLevel = "Near Miss";

    // Check for fatality - Level 5 Critical
    if (data.injuryClassification.isFatality === true) {
      incidentClassificationLevel = "Level 5 - Critical - Critical Incident";
    }
    // Check for permanent disability - Level 4 High
    else if (data.injuryClassification.isPermanentDisability === true) {
      incidentClassificationLevel = "Level 4 - High - High Severity Incident";
    }
    // Check for lost time incident - Level 3 Medium
    else if (data.injuryClassification.isLostTimeIncident === true) {
      incidentClassificationLevel = "Level 3 - Medium - LTI - Lost Time Incident";
    }
    // Check for medical treatment - Level 2 Low
    else if (data.injuryClassification.isMedicalTreatment === true) {
      incidentClassificationLevel = "Level 2 - Low - MTI - Medical Treatment Incident";
    }
    // Check for first aid - Level 1 Very Low
    else if (data.injuryClassification.isFirstAid === true) {
      incidentClassificationLevel = "Level 1 - Very Low - FAI - First Aid Incident";
    }

    // Determine potential severity level based on answers
    let potentialSeverityLevel = "Not Applicable";

    if (data.potentiallySerious.couldResultInFatality ||
        data.potentiallySerious.couldResultInPermanentDisability) {
      potentialSeverityLevel = "Level 4 - High - Potentially Serious Incident";

      // If the actual incident level is 3 or below, the potential severity takes precedence
      const actualLevel = parseInt(incidentClassificationLevel.split(" ")[1]);
      if (actualLevel <= 3 || isNaN(actualLevel)) {
        incidentClassificationLevel = "Level 4 - High - Potentially Serious Incident";
      }
    }

    // Log the form data for debugging
    console.log("Form data being submitted:", JSON.stringify(data, null, 2));

    // Log specific sections for debugging
    console.log("Location data being submitted:", data.location);
    console.log("Actions Taken data being submitted:", {
      immediateActionDate: data.immediateActionDate,
      immediateActionsTaken: data.immediateActionsTaken,
      legalClassification: data.legalClassification
    });

    // Create a deep copy of the incident to ensure we don't lose any data
    const updatedIncident = JSON.parse(JSON.stringify(incident));

    // Update the incident with the form data, ensuring we retain all existing data
    // Updated initial details
    updatedIncident.incidentTitle = data.incidentTitle;
    updatedIncident.description = data.description;
    updatedIncident.incidentDate = data.incidentDate;
    updatedIncident.incidentTime = data.incidentTime;
    updatedIncident.incidentType = data.incidentType;
    updatedIncident.incidentCategory = data.incidentCategory;

    // Map location data to both old and new field names for compatibility
    updatedIncident.locationCountry = data.location.country;
    updatedIncident.locationCity = data.location.city;
    updatedIncident.locationBusinessUnit = data.location.businessUnit;
    updatedIncident.locationProject = data.location.projectDcOps;
    updatedIncident.locationDetails = data.location.levelAndLocation;

    // Keep old field names for backward compatibility
    updatedIncident.country = data.location.country;
    updatedIncident.city = data.location.city;
    updatedIncident.workplaceActivity = data.location.businessUnit;
    updatedIncident.projectDcOps = data.location.projectDcOps;
    updatedIncident.levelAndLocation = data.location.levelAndLocation;

    // Injury Classification - ensure we properly merge with existing data
    updatedIncident.injuryClassification = {
      ...(updatedIncident.injuryClassification || {}),
      isFatality: data.injuryClassification.isFatality,
      isPermanentDisability: data.injuryClassification.isPermanentDisability,
      isLostTimeIncident: data.injuryClassification.isLostTimeIncident,
      isMedicalTreatment: data.injuryClassification.isMedicalTreatment,
      isFirstAid: data.injuryClassification.isFirstAid,
    };

    updatedIncident.incidentClassificationLevel = incidentClassificationLevel;
    updatedIncident.isWorkRelated = data.injuryClassification.isWorkRelated;
    updatedIncident.lossOfConsciousness = data.injuryClassification.lossOfConsciousness;
    updatedIncident.isDangerousOccurrence = data.injuryClassification.isDangerousOccurrence;

    // Classification and additional details
    updatedIncident.potentiallySerious = {
      ...(updatedIncident.potentiallySerious || {}),
      couldResultInFatality: data.potentiallySerious.couldResultInFatality,
      couldResultInPermanentDisability: data.potentiallySerious.couldResultInPermanentDisability,
    };

    updatedIncident.potentialSeverityLevel = potentialSeverityLevel;
    updatedIncident.propertyDamage = data.propertyDamage;
    updatedIncident.propertyDamageDetails = data.propertyDamageDetails;
    updatedIncident.surfaceType = data.surfaceType;
    updatedIncident.surfaceCondition = data.surfaceCondition;
    updatedIncident.lighting = data.lighting;
    updatedIncident.weatherCondition = data.weatherCondition;
    updatedIncident.reportableToAuthorities = data.reportableToAuthorities;
    updatedIncident.reportableDetails = data.reportableDetails;
    updatedIncident.immediateActionDate = data.immediateActionDate;
    updatedIncident.immediateActionsTaken = data.immediateActionsTaken;
    updatedIncident.legalClassification = data.legalClassification;

    // Merge photos without losing existing ones
    updatedIncident.photos = [
      ...(updatedIncident.photos || []),
      ...data.photos.map(file => URL.createObjectURL(file))
    ];

    // Control Measures fields - ensure we properly merge with existing data
    updatedIncident.incidentGMS = data.incidentGMS;
    updatedIncident.stopWorkOrder = data.stopWorkOrder;
    updatedIncident.rootCauseAnalysis = data.rootCauseAnalysis;
    updatedIncident.controlMeasuresRequired = data.controlMeasuresRequired;
    updatedIncident.controlMeasuresDescription = data.controlMeasuresDescription;
    updatedIncident.controlMeasures = data.controlMeasures;
    updatedIncident.riskAssessmentRequired = data.riskAssessmentRequired;
    updatedIncident.riskAssessmentDescription = data.riskAssessmentDescription;
    updatedIncident.riskAssessments = data.riskAssessments;

    // Investigation fields
    updatedIncident.investigationDetails = data.investigationDetails;
    updatedIncident.detailedInvestigation = data.detailedInvestigation;
    updatedIncident.comprehensiveInvestigation = data.comprehensiveInvestigation;

    // requiresAction will be set based on the workflow stage and user role below



    // Set the reviewer name when a reviewer takes action
    updatedIncident.reviewedBy = userRole === 'reviewer' ? userName : updatedIncident.reviewedBy;
    updatedIncident.actionTakenAt = new Date();
    updatedIncident.actionTakenBy = userName;

    // Status and stage depend on the user role and current workflow stage
    if (workflowStage === 'comprehensive') {
      // When comprehensive investigation is submitted, close the incident
      updatedIncident.status = "closed";
      updatedIncident.stage = 'Detailed Investigation & Analysis Completed';
      updatedIncident.workflowStage = 'completed';
      updatedIncident.investigationStatus = 'completed';
      updatedIncident.comprehensiveInvestigationStatus = 'completed';
      updatedIncident.closedAt = new Date();
      updatedIncident.closedBy = userName;
      updatedIncident.requiresAction = false; // No further action needed when closed
    } else if (workflowStage === 'investigation' && userRole === 'reviewer') {
      // When reviewer submits investigation, check if comprehensive investigation is needed
      if (data.comprehensiveInvestigation && Object.keys(data.comprehensiveInvestigation).length > 0) {
        // Move to comprehensive investigation stage
        updatedIncident.status = "comprehensive";
        updatedIncident.stage = 'Detailed Investigation & Analysis in Progress';
        updatedIncident.workflowStage = 'comprehensive';
        updatedIncident.investigationStatus = 'completed';
        updatedIncident.comprehensiveInvestigationStatus = 'in-progress';
        updatedIncident.requiresAction = true; // Requires comprehensive investigation
      } else {
        // Close the incident if no comprehensive investigation needed
        updatedIncident.status = "closed";
        updatedIncident.stage = 'Preliminary Analysis Completed';
        updatedIncident.workflowStage = 'completed';
        updatedIncident.investigationStatus = 'completed';
        updatedIncident.closedAt = new Date();
        updatedIncident.closedBy = userName;
        updatedIncident.requiresAction = false; // No further action needed when closed
      }
    } else if (workflowStage === 'investigation' && userRole === 'reporter') {
      // When reporter submits investigation, keep it in investigation stage
      updatedIncident.status = "submitted";
      updatedIncident.stage = 'Preliminary Analysis in Progress';
      updatedIncident.workflowStage = 'investigation';
      updatedIncident.investigationStatus = 'in-progress';
      updatedIncident.requiresAction = true; // Still requires reviewer action
    } else {
      // Original logic for non-investigation submissions
      updatedIncident.status = userRole === 'reviewer' ? "submitted" : "under-review"; // Reviewers set to submitted (Reported), reporters to under-review
      updatedIncident.workflowStage = userRole === 'reviewer' ? 'investigation' : 'review';
      updatedIncident.stage = userRole === 'reviewer' ? 'Preliminary Analysis in Progress' : 'Supplementary information in Progress';
      updatedIncident.investigationStatus = userRole === 'reviewer' ? 'not-started' : updatedIncident.investigationStatus;
      updatedIncident.requiresAction = true; // Requires action for normal workflow
    }

    // Original reporter information is already preserved in the spread operator above
    // No need to reassign these values to themselves

    // Ensure we preserve any investigation details that might exist
    if (updatedIncident.investigationDetails) {
      // Keep existing investigation details intact
      updatedIncident.investigationDetails = {
        ...updatedIncident.investigationDetails,
        // Any specific updates to investigation details would go here
      };
    }

    // Ensure we preserve any comprehensive investigation details that might exist
    if (updatedIncident.comprehensiveInvestigation) {
      // Keep existing comprehensive investigation details intact
      updatedIncident.comprehensiveInvestigation = {
        ...updatedIncident.comprehensiveInvestigation,
        // Any specific updates to comprehensive investigation details would go here
      };
    }

    // Log the updated incident for debugging
    console.log("Updated incident:", JSON.stringify(updatedIncident, null, 2));



    // Check if this is an investigation submission based on workflow stage
    if (workflowStage === 'comprehensive') {
      toast.success("Detailed Investigation & Analysis completed and incident closed!", {
        description: "The detailed investigation & analysis has been submitted and the incident status is now 'Closed' with stage 'Detailed Investigation & Analysis Completed'.",
      });
    } else if (workflowStage === 'investigation' && userRole === 'reviewer') {
      // Check if comprehensive investigation is needed
      if (data.comprehensiveInvestigation && Object.keys(data.comprehensiveInvestigation).length > 0) {
        toast.success("Investigation completed - Moving to Detailed Investigation & Analysis!", {
          description: "The preliminary investigation has been completed. The incident is now in 'Detailed Investigation & Analysis in Progress' stage.",
        });
      } else {
        toast.success("Investigation completed and incident closed!", {
          description: "The investigation has been submitted and the incident status is now 'Closed' with stage 'Preliminary Analysis Completed'.",
        });
      }
    } else if (workflowStage === 'investigation' && userRole === 'reporter') {
      toast.success("Investigation submitted successfully!", {
        description: "Your investigation report has been submitted and the incident has been updated.",
      });
    } else if (userRole === 'reviewer') {
      toast.success("Incident submitted successfully!", {
        description: "The incident is now in the 'Preliminary Analysis in Progress' stage. Both Reporter and Reviewer can now update Investigation Details in their 'My Actions' tab.",
      });
    } else {
      toast.success("Incident updated successfully!", {
        description: "Your changes have been saved and will be visible to the reviewer.",
      });
    }

    onActionComplete(updatedIncident);
    onOpenChange(false);
  };

  // Mock data for dropdowns
  const surfaceTypes = [
    { value: "concrete", label: "Concrete" },
    { value: "carpet", label: "Carpet" },
    { value: "tile", label: "Tile" },
    { value: "wood", label: "Wood" },
    { value: "metal", label: "Metal" },
    { value: "asphalt", label: "Asphalt" },
    { value: "grass", label: "Grass" },
    { value: "other", label: "Other" },
  ];

  const surfaceConditions = [
    { value: "dry", label: "Dry" },
    { value: "wet", label: "Wet" },
    { value: "icy", label: "Icy" },
    { value: "oily", label: "Oily/Slippery" },
    { value: "damaged", label: "Damaged" },
    { value: "uneven", label: "Uneven" },
    { value: "other", label: "Other" },
  ];

  const lightingOptions = [
    { value: "good", label: "Good" },
    { value: "adequate", label: "Adequate" },
    { value: "poor", label: "Poor" },
    { value: "none", label: "None" },
  ];

  const weatherConditions = [
    { value: "clear", label: "Clear" },
    { value: "cloudy", label: "Cloudy" },
    { value: "rain", label: "Rain" },
    { value: "snow", label: "Snow" },
    { value: "fog", label: "Fog" },
    { value: "wind", label: "Windy" },
    { value: "storm", label: "Storm" },
    { value: "indoor", label: "Indoor (N/A)" },
  ];

  const legalClassifications = [
    { value: "normal", label: "Normal" },
    { value: "client-privilege", label: "Client Privilege" },
  ];



  // Custom styles for form elements to match the Report an Incident form
  const formLabelClass = "text-base font-medium mb-2";
  const formInputClass = "w-full";
  const formSectionTitleClass = "flex items-center gap-3 mb-6";
  const formSectionIconClass = "flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[90vw] max-h-[90vh] overflow-y-auto p-0">
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold">
            Take Action on Incident: {incident?.id || ''}
          </DialogTitle>
          <DialogDescription>
            Please review and update the incident information as needed.
          </DialogDescription>
        </DialogHeader>

        <div className="px-6 pb-6">
          {/* Workflow Step Indicator */}
          <div className={cn(
            "mb-6 rounded-lg",
            workflowStage !== 'initial' ? "bg-green-50 border border-green-200 p-4" : ""
          )}>
            <WorkflowStepIndicator
              currentStage={workflowStage as 'initial' | 'review' | 'investigation' | 'comprehensive'}
              userRole={userRole}
              isInitialReportCompleted={isInitialReportCompleted}
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            {/* Show different tabs based on the workflow stage */}
            {workflowStage === 'comprehensive' ? (
              // For Detailed Investigation & Analysis phase (Step 4), show only comprehensive investigation tab
              <TabsList className="grid grid-cols-1 mb-6">
                <TabsTrigger value="comprehensive-investigation">Detailed Investigation & Analysis</TabsTrigger>
              </TabsList>
            ) : workflowStage === 'investigation' ? (
              // For Investigation phase (Step 3), show investigation-related tabs for both Reporter and Reviewer
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="investigation">Investigation & Analysis</TabsTrigger>
                <TabsTrigger value="control-measures">Control Measures</TabsTrigger>
              </TabsList>
            ) : (
              // For other phases, show the standard tabs
              <TabsList className={`grid ${userRole === 'reviewer' ? 'grid-cols-6' : 'grid-cols-5'} mb-6`}>
                <TabsTrigger value="details">Incident Details</TabsTrigger>
                <TabsTrigger value="classification">Classification</TabsTrigger>
                <TabsTrigger value="conditions">Conditions</TabsTrigger>
                <TabsTrigger value="actions">Immediate Actions Taken</TabsTrigger>
                <TabsTrigger value="attachments">Attachments</TabsTrigger>
                {userRole === 'reviewer' && (
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                )}
              </TabsList>
            )}

            <Form {...form}>
              <form onSubmit={(e) => {
                // Only allow form submission from the Complete Action button
                // This prevents accidental submission when pressing Enter
                e.preventDefault();
              }} className="space-y-6">
                <TabsContent value="details" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Info size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Incident Information Required in Initial Reporting</h2>
                    </div>

                    <div className="space-y-6">
                      {/* Incident Title Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-purple-100 text-purple-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-heading-1"><path d="M4 12h8"/><path d="M4 18V6"/><path d="M12 18V6"/><path d="m17 12 3-2v8"/></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Incident Title</h3>
                        </div>

                        {/* Incident Title */}
                        <FormField
                          control={form.control}
                          name="incidentTitle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={formLabelClass}>1. Incident Title</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Enter a descriptive title for the incident"
                                  {...field}
                                  className={`${formInputClass} h-12 border-2 bg-white`}
                                />
                              </FormControl>
                              <FormDescription>
                                Free writing field - provide a clear, concise title
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Date and Time Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-blue-100 text-blue-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-calendar-clock"><path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.5"/><path d="M16 2v4"/><path d="M8 2v4"/><path d="M3 10h18"/><circle cx="17" cy="16" r="3"/><path d="M17 14.5v1.5h1.5"/></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Date and Time</h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                          {/* Incident Date */}
                          <FormField
                            control={form.control}
                            name="incidentDate"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>2. Incident Date</FormLabel>
                                <FormControl>
                                  <Input
                                    type="date"
                                    {...field}
                                    value={field.value ? format(field.value, "yyyy-MM-dd") : ""}
                                    onChange={(e) => {
                                      const dateValue = e.target.value ? new Date(e.target.value) : new Date();
                                      field.onChange(dateValue);
                                    }}
                                    className={`${formInputClass} h-12 border-2 bg-white`}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Select the date when the incident occurred
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Incident Time */}
                          <FormField
                            control={form.control}
                            name="incidentTime"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>Incident Time</FormLabel>
                                <div className="relative">
                                  <FormControl>
                                    <TimePicker
                                      value={field.value}
                                      onChange={field.onChange}
                                      className={`h-12 border-2 bg-white ${formInputClass}`}
                                    />
                                  </FormControl>
                                </div>
                                <FormDescription>
                                  Time picker - select the time when the incident occurred
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Incident Type Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-amber-100 text-amber-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-alert-triangle"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Incident Type</h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                          <FormField
                            control={form.control}
                            name="incidentType"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>3. Incident Type</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder="Select incident type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="safety">Safety</SelectItem>
                                    <SelectItem value="environmental">Environmental</SelectItem>
                                    <SelectItem value="health">Health</SelectItem>
                                    <SelectItem value="nearMiss">Near Miss</SelectItem>
                                    <SelectItem value="propertyDamage">Property Damage</SelectItem>
                                    <SelectItem value="security">Security</SelectItem>
                                    <SelectItem value="quality">Quality</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Select the primary type of incident
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="incidentCategory"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>4. Incident Category</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder="Select incident category" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="fire">Fire</SelectItem>
                                    <SelectItem value="slip">Slip</SelectItem>
                                    <SelectItem value="fall">Fall</SelectItem>
                                    <SelectItem value="electrical">Electrical Hazard</SelectItem>
                                    <SelectItem value="chemical">Chemical Spill</SelectItem>
                                    <SelectItem value="vehicle">Vehicle Accident</SelectItem>
                                    <SelectItem value="machinery">Machinery</SelectItem>
                                    <SelectItem value="tool">Tool Related</SelectItem>
                                    <SelectItem value="ergonomic">Ergonomic</SelectItem>
                                    <SelectItem value="other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Select the specific category of the incident
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Description Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-green-100 text-green-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-text"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" x2="8" y1="13" y2="13"/><line x1="16" x2="8" y1="17" y2="17"/><line x1="10" x2="8" y1="9" y2="9"/></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Description</h3>
                        </div>

                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={formLabelClass}>5. Brief Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  className="min-h-24 border-2 bg-white"
                                  placeholder="Provide a detailed description of the incident..."
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Provide a clear description of what happened, including relevant details
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Location Group */}
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="p-1.5 rounded-full bg-indigo-100 text-indigo-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-map-pin"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/><circle cx="12" cy="10" r="3"/></svg>
                          </div>
                          <h3 className="text-sm font-medium text-gray-700">Location Information</h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                          <FormField
                            control={form.control}
                            name="location.country"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>6. Country</FormLabel>
                                <Select
                                  onValueChange={(value) => {
                                    field.onChange(value);
                                    // Reset city when country changes to avoid invalid city selections
                                    form.setValue("location.city", "");
                                  }}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder="Select country" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="singapore">Singapore</SelectItem>
                                    <SelectItem value="malaysia">Malaysia</SelectItem>
                                    <SelectItem value="philippines">Philippines</SelectItem>
                                    <SelectItem value="thailand">Thailand</SelectItem>
                                    <SelectItem value="indonesia">Indonesia</SelectItem>
                                    <SelectItem value="other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Select the country where the incident occurred
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="location.city"
                            render={({ field }) => {
                              const selectedCountry = form.watch("location.country") || "other";
                              const cityOptions = cities[selectedCountry as keyof typeof cities] || cities.other;

                              return (
                                <FormItem>
                                  <FormLabel className={formLabelClass}>7. City</FormLabel>
                                  <Select
                                    onValueChange={field.onChange}
                                    value={field.value || ""}
                                    disabled={!selectedCountry}
                                  >
                                    <FormControl>
                                      <SelectTrigger className="h-12 border-2 bg-white">
                                        <SelectValue placeholder="Select city" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {cityOptions.map((city) => (
                                        <SelectItem key={city.value} value={city.value}>
                                          {city.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormDescription>
                                    Select the city where the incident occurred
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              );
                            }}
                          />
                        </div>

                        <div className="grid md:grid-cols-2 gap-x-6 gap-y-4 mt-4">
                          <FormField
                            control={form.control}
                            name="location.businessUnit"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>8. Business Unit</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value || ""}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder="Select business unit" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {businessUnits.map((unit) => (
                                      <SelectItem key={unit.value} value={unit.value}>
                                        {unit.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Select the business unit where the incident occurred
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="location.projectDcOps"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>9. Project/DC Ops</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value || ""}
                                >
                                  <FormControl>
                                    <SelectTrigger className="h-12 border-2 bg-white">
                                      <SelectValue placeholder="Select project or DC ops" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {projectOptions.map((project) => (
                                      <SelectItem key={project.value} value={project.value}>
                                        {project.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Select the project or DC ops where the incident occurred
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="mt-4">
                          <FormField
                            control={form.control}
                            name="location.levelAndLocation"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>10. Level and Location</FormLabel>
                                <FormControl>
                                  <Input className={`h-12 border-2 bg-white ${formInputClass}`} {...field} value={field.value || ""} />
                                </FormControl>
                                <FormDescription>
                                  Provide specific details about the level and exact location
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="classification" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <AlertTriangle size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Injury Classification</h2>
                    </div>

                    <div className="space-y-6 border p-4 rounded-md">
                      <p className="text-sm text-muted-foreground mb-4">
                        Update the injury classification data as needed based on your assessment.
                      </p>

                      {/* Work Related Question */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isWorkRelated"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">1. Is this incident work related?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Work-related incidents occur during work activities or as a result of performing work duties. Only work-related incidents are counted in official incident statistics.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Loss of Consciousness */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.lossOfConsciousness"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">2. Was there a loss of consciousness?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Loss of consciousness refers to any period where the injured person was unresponsive, regardless of duration. This is a serious medical condition that requires immediate attention.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Dangerous Occurrence */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isDangerousOccurrence"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">3. Was this as a result of Dangerous Occurrence?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A Dangerous Occurrence is an unplanned, uncontrolled event that had the potential to cause injury, ill health or damage, but did not actually do so. Examples include structural collapses, explosions, or equipment failures.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="pt-2 pb-1">
                        <h3 className="text-lg font-medium">Did this incident result in:</h3>
                      </div>

                      {/* Fatality */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isFatality"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">A fatality?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A fatality is a death resulting from a work-related incident or occupational illness. This is classified as a Level 5 Critical Incident and requires immediate reporting to authorities.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Permanent Disability */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isPermanentDisability"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">An injury or occupational illness resulting in permanent disability?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Permanent disability refers to any injury or illness that results in permanent impairment of bodily functions, including loss of limbs, paralysis, or permanent damage to organs or senses. This is classified as a Level 4 High Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Lost Time Incident */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isLostTimeIncident"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Lost Time Incident (LTI)?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A Lost Time Incident (LTI) is an injury or illness that results in the employee being unable to work for one or more scheduled workdays after the day of the incident. This is classified as a Level 3 Medium Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Medical Treatment */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isMedicalTreatment"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Medical Treatment of Illness/Injury?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Medical Treatment Injury (MTI) refers to injuries that require treatment beyond first aid, administered by a physician or other medical professional. This is classified as a Level 2 Low Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* First Aid */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isFirstAid"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Need to administer First Aid?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>First Aid Injury (FAI) refers to minor injuries that can be treated on-site using basic first aid supplies and do not require professional medical care. This is classified as a Level 1 Very Low Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => field.onChange(true)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === true
                                      ? 'bg-green-500 border-green-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">Yes</span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => field.onChange(false)}
                                  className={`
                                    relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                                    ${field.value === false
                                      ? 'bg-red-500 border-red-500 text-white shadow-md'
                                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                                    }
                                  `}
                                >
                                  <span className="text-xs font-medium">No</span>
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="text-sm text-muted-foreground italic mt-4 p-3 bg-gray-50 rounded-md">
                        <p>If all the answers above are "No", then the incident is classified as a "NEAR MISS".</p>
                      </div>

                      {/* Incident Classification Result */}
                      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <h3 className="font-bold text-lg flex items-center">
                          <AlertTriangle className="mr-2 h-5 w-5 text-yellow-600" />
                          Incident Classification Result
                        </h3>
                        <p className="mt-2 font-medium">
                          {form.getValues("injuryClassification.isFatality") === true && "Level 5 – Critical Incident"}
                          {form.getValues("injuryClassification.isPermanentDisability") === true && form.getValues("injuryClassification.isFatality") !== true && "Level 4 – High Severity Incident"}
                          {form.getValues("injuryClassification.isLostTimeIncident") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && "Level 3 – LTI - Medium Severity Incident "}
                          {form.getValues("injuryClassification.isMedicalTreatment") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && "Level 2 – Low Severity Incident (MTI)"}
                          {form.getValues("injuryClassification.isFirstAid") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && form.getValues("injuryClassification.isMedicalTreatment") !== true && "Level 1 – Very Low Severity Incident (FAI)"}
                          {form.getValues("injuryClassification.isFatality") === false && form.getValues("injuryClassification.isPermanentDisability") === false && form.getValues("injuryClassification.isLostTimeIncident") === false && form.getValues("injuryClassification.isMedicalTreatment") === false && form.getValues("injuryClassification.isFirstAid") === false && "NEAR MISS"}
                        </p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <FormField
                        control={form.control}
                        name="propertyDamage"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Property Damage</FormLabel>
                              <FormDescription>
                                Check if this incident resulted in property damage
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {watchPropertyDamage && (
                      <div className="mt-4">
                        <FormField
                          control={form.control}
                          name="propertyDamageDetails"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Property Damage Details</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Describe the property damage..."
                                  className="min-h-24"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="conditions" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Info size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Environmental Conditions</h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="surfaceType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Surface Type</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select surface type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {surfaceTypes.map((type) => (
                                  <SelectItem key={type.value} value={type.value}>
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="surfaceCondition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Surface Condition</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select surface condition" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {surfaceConditions.map((condition) => (
                                  <SelectItem key={condition.value} value={condition.value}>
                                    {condition.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="lighting"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Lighting</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select lighting condition" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {lightingOptions.map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="weatherCondition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Weather Condition</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select weather condition" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {weatherConditions.map((condition) => (
                                  <SelectItem key={condition.value} value={condition.value}>
                                    {condition.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="mt-6">
                      <FormField
                        control={form.control}
                        name="reportableToAuthorities"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Is this incident reportable to local authority and/or customer?</FormLabel>
                              <FormDescription>
                                Check if this incident needs to be reported to regulatory bodies, police, etc.
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {watchReportableToAuthorities && (
                      <div className="mt-4">
                        <FormField
                          control={form.control}
                          name="reportableDetails"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Reporting Details</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Provide details about which authorities need to be notified and why..."
                                  className="min-h-24"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="actions" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Check size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Immediate Actions Taken</h2>
                    </div>

                    <div className="space-y-6">
                      <FormField
                        control={form.control}
                        name="immediateActionDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel className={formLabelClass}>Action Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant={"outline"}
                                    className={cn(
                                      "w-full pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP")
                                    ) : (
                                      <span>Select date</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => date > new Date()}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="immediateActionsTaken"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Actions Taken</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe the immediate actions taken to address the incident..."
                                className="min-h-32"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="legalClassification"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Legal Classification</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select legal classification" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {legalClassifications.map((classification) => (
                                  <SelectItem key={classification.value} value={classification.value}>
                                    {classification.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />


                    </div>
                  </div>
                </TabsContent>



                <TabsContent value="attachments" className="space-y-6">
                  <div className="form-section">
                    <div className={formSectionTitleClass}>
                      <div className={formSectionIconClass}>
                        <Upload size={20} />
                      </div>
                      <h2 className="text-xl font-semibold">Upload Photos/Files</h2>
                    </div>

                    <p className="mb-4 text-gray-600">
                      Upload photos of the incident to provide visual evidence. Photos significantly help in
                      investigating and addressing the incident properly.
                    </p>

                    <FormField
                      control={form.control}
                      name="photos"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <PhotoUpload
                              onPhotosChange={(photos) => field.onChange(photos)}
                              existingPhotos={field.value as File[]}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>

                {/* Investigation tabs - shown when workflowStage is 'investigation' */}
                {workflowStage === 'investigation' && (
                  <>
                    <TabsContent value="investigation" className="space-y-6">
                      {/* Basic Investigation Section */}
                      <div className="form-section">
                        <div className={formSectionTitleClass}>
                          <div className={formSectionIconClass}>
                            <FileText size={20} />
                          </div>
                          <h2 className="text-xl font-semibold">Preliminary Investigation & Analysis </h2>
                        </div>

                        <div className="space-y-6">
                          <FormField
                            control={form.control}
                            name="investigationDetails"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>Investigation Details</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Enter detailed investigation findings..."
                                    className="min-h-[150px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Provide a comprehensive description of your investigation findings.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="rootCauseAnalysis"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className={formLabelClass}>Root Cause Analysis</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Enter root cause analysis..."
                                    className="min-h-[150px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Identify and document the root causes that contributed to this incident.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Detailed Investigation Section */}
                      <div className="form-section">
                        {/* <div className={formSectionTitleClass}>
                          <div className={formSectionIconClass}>
                            <Microscope size={20} />
                          </div>
                          <h2 className="text-xl font-semibold"> Investigation and Analysis</h2>
                        </div> */}

                        {/* <div className="p-4 border rounded-md bg-blue-50 mb-4">
                          <div className="flex items-start gap-2">
                            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-blue-800">Complete Investigation Details</p>
                              <p className="text-sm text-blue-700 mt-1">
                                Please complete all sections of the detailed investigation form below. This information is critical for understanding the incident and preventing future occurrences.
                              </p>
                            </div>
                          </div>
                        </div> */}

                        {/* Use the InvestigationAnalysisForm component with isNested=true */}
                        <InvestigationAnalysisForm
                          incident={incident}
                          isNested={true}
                          onSubmit={(data) => {
                            // Update the form with the data from the InvestigationAnalysisForm
                            console.log("IncidentActionDialog - onSubmit callback called with data:", data);
                            form.setValue('detailedInvestigation', data, { shouldValidate: true });
                            console.log("IncidentActionDialog - After setting form value, form values:", form.getValues());
                          }}
                        />
                      </div>
                    </TabsContent>

                    <TabsContent value="control-measures" className="space-y-6">
                      <div className="form-section">
                        <div className={formSectionTitleClass}>
                          <div className={formSectionIconClass}>
                            <FileText size={20} />
                          </div>
                          <h2 className="text-xl font-semibold">Control Measures</h2>
                        </div>

                        <div className="space-y-6 mb-8">
                          <FormField
                            control={form.control}
                            name="controlMeasuresRequired"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4 bg-slate-50">
                                <div className="space-y-1 flex-1">
                                  <FormLabel className="text-base">
                                    Are control measures required?
                                  </FormLabel>
                                  <FormDescription>
                                    Toggle this switch if additional control measures are needed to prevent recurrence.
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <div className={`w-12 h-6 rounded-full p-1 transition-colors ${field.value ? 'bg-green-500' : 'bg-gray-300'}`}>
                                    <div
                                      className={`bg-white w-4 h-4 rounded-full shadow-md transform transition-transform ${field.value ? 'translate-x-6' : ''}`}
                                      onClick={() => field.onChange(!field.value)}
                                    ></div>
                                  </div>
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          {form.watch("controlMeasuresRequired") && (
                            <div className="border rounded-md p-4 bg-slate-50">
                              <div className="flex justify-between items-center mb-4">
                                <h3 className="font-medium">Control Measures to be Implemented</h3>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const currentMeasures = form.getValues("controlMeasures") || [];
                                    form.setValue("controlMeasures", [...currentMeasures, { description: "", dueDate: undefined, personResponsible: "" }]);
                                  }}
                                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                                >
                                  Add Measure
                                </Button>
                              </div>

                              {form.getValues("controlMeasures")?.length === 0 && (
                                <div className="text-center py-4 text-slate-500">
                                  No control measures added. Click "Add Measure" to add one.
                                </div>
                              )}

                              <div className="grid grid-cols-3 gap-4 mb-2 px-3">
                                <div className="font-medium text-sm text-gray-600">Corrective/Control measures:</div>
                                <div className="font-medium text-sm text-gray-600">Due Date</div>
                                <div className="font-medium text-sm text-gray-600">Person Responsible</div>
                              </div>

                              {form.getValues("controlMeasures")?.map((_, index) => (
                                <div key={index} className="grid grid-cols-3 gap-4 items-center mb-3 pb-3 border-b last:border-0 bg-white p-3 rounded-md shadow-sm">
                                  <FormField
                                    control={form.control}
                                    name={`controlMeasures.${index}.description`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <FormControl>
                                          <Input
                                            placeholder="Enter control measure..."
                                            {...field}
                                            className="h-10"
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`controlMeasures.${index}.dueDate`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <Popover>
                                          <PopoverTrigger asChild>
                                            <FormControl>
                                              <Button
                                                variant={"outline"}
                                                className={cn(
                                                  "w-full pl-3 text-left font-normal h-10",
                                                  !field.value && "text-muted-foreground"
                                                )}
                                              >
                                                {field.value ? (
                                                  format(field.value, "dd-MM-yyyy")
                                                ) : (
                                                  <span>Select date</span>
                                                )}
                                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                              </Button>
                                            </FormControl>
                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                            <Calendar
                                              mode="single"
                                              selected={field.value}
                                              onSelect={field.onChange}
                                              initialFocus
                                            />
                                          </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <div className="flex items-center gap-2">
                                    <FormField
                                      control={form.control}
                                      name={`controlMeasures.${index}.personResponsible`}
                                      render={({ field }) => (
                                        <FormItem className="flex-1 mb-0">
                                          <Select onValueChange={field.onChange} value={field.value || ""}>
                                            <FormControl>
                                              <SelectTrigger className="h-10">
                                                <SelectValue placeholder="Choose" />
                                              </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                              <SelectItem value="safety_manager">Safety Manager</SelectItem>
                                              <SelectItem value="facility_manager">Facility Manager</SelectItem>
                                              <SelectItem value="operations_director">Operations Director</SelectItem>
                                              <SelectItem value="hr_manager">HR Manager</SelectItem>
                                              <SelectItem value="department_head">Department Head</SelectItem>
                                              <SelectItem value="site_manager">Site Manager</SelectItem>
                                              <SelectItem value="project_manager">Project Manager</SelectItem>
                                            </SelectContent>
                                          </Select>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      className="h-10 w-10 rounded-full text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                                      onClick={() => {
                                        const currentMeasures = form.getValues("controlMeasures") || [];
                                        const updatedMeasures = currentMeasures.filter((_, i) => i !== index);
                                        form.setValue("controlMeasures", updatedMeasures);
                                      }}
                                    >
                                      <X className="h-5 w-5" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Risk Assessment Section */}
                        <div className="space-y-6 mb-8">
                          <FormField
                            control={form.control}
                            name="riskAssessmentRequired"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4 bg-slate-50">
                                <div className="space-y-1 flex-1">
                                  <FormLabel className="text-base">
                                    Is risk assessments and safe working procedures need to be reviewed and updated?
                                  </FormLabel>
                                  <FormDescription>
                                    Toggle this switch if risk assessments and safe working procedures need to be reviewed and updated.
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <div className={`w-12 h-6 rounded-full p-1 transition-colors ${field.value ? 'bg-green-500' : 'bg-gray-300'}`}>
                                    <div
                                      className={`bg-white w-4 h-4 rounded-full shadow-md transform transition-transform ${field.value ? 'translate-x-6' : ''}`}
                                      onClick={() => field.onChange(!field.value)}
                                    ></div>
                                  </div>
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          {form.watch("riskAssessmentRequired") && (
                            <div className="border rounded-md p-4 bg-slate-50">
                              <div className="flex justify-between items-center mb-4">
                                <h3 className="font-medium">Risk Assessments to be Reviewed/Updated</h3>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const currentAssessments = form.getValues("riskAssessments") || [];
                                    form.setValue("riskAssessments", [...currentAssessments, { name: "", dueDate: undefined, personResponsible: "" }]);
                                  }}
                                  className="bg-purple-50 text-purple-600 border-purple-200 hover:bg-purple-100"
                                >
                                  Add Risk Assessment
                                </Button>
                              </div>

                              {form.getValues("riskAssessments")?.length === 0 && (
                                <div className="text-center py-4 text-slate-500">
                                  No risk assessments added. Click "Add Risk Assessment" to add one.
                                </div>
                              )}

                              <div className="grid grid-cols-3 gap-4 mb-2 px-3">
                                <div className="font-medium text-sm text-gray-600">Risk Assessment Name:</div>
                                <div className="font-medium text-sm text-gray-600">Due Date</div>
                                <div className="font-medium text-sm text-gray-600">Person Responsible</div>
                              </div>

                              {form.getValues("riskAssessments")?.map((_, index) => (
                                <div key={index} className="grid grid-cols-3 gap-4 items-center mb-3 pb-3 border-b last:border-0 bg-white p-3 rounded-md shadow-sm">
                                  <FormField
                                    control={form.control}
                                    name={`riskAssessments.${index}.name`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <FormControl>
                                          <Input
                                            placeholder="Enter risk assessment name..."
                                            {...field}
                                            className="h-10"
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`riskAssessments.${index}.dueDate`}
                                    render={({ field }) => (
                                      <FormItem className="mb-0">
                                        <Popover>
                                          <PopoverTrigger asChild>
                                            <FormControl>
                                              <Button
                                                variant={"outline"}
                                                className={cn(
                                                  "w-full pl-3 text-left font-normal h-10",
                                                  !field.value && "text-muted-foreground"
                                                )}
                                              >
                                                {field.value ? (
                                                  format(field.value, "dd-MM-yyyy")
                                                ) : (
                                                  <span>Select date</span>
                                                )}
                                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                              </Button>
                                            </FormControl>
                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                            <Calendar
                                              mode="single"
                                              selected={field.value}
                                              onSelect={field.onChange}
                                              initialFocus
                                            />
                                          </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <div className="flex items-center gap-2">
                                    <FormField
                                      control={form.control}
                                      name={`riskAssessments.${index}.personResponsible`}
                                      render={({ field }) => (
                                        <FormItem className="flex-1 mb-0">
                                          <Select onValueChange={field.onChange} value={field.value || ""}>
                                            <FormControl>
                                              <SelectTrigger className="h-10">
                                                <SelectValue placeholder="Choose" />
                                              </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                              <SelectItem value="safety_manager">Safety Manager</SelectItem>
                                              <SelectItem value="facility_manager">Facility Manager</SelectItem>
                                              <SelectItem value="operations_director">Operations Director</SelectItem>
                                              <SelectItem value="hr_manager">HR Manager</SelectItem>
                                              <SelectItem value="department_head">Department Head</SelectItem>
                                              <SelectItem value="site_manager">Site Manager</SelectItem>
                                              <SelectItem value="project_manager">Project Manager</SelectItem>
                                            </SelectContent>
                                          </Select>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      className="h-10 w-10 rounded-full text-red-500 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                                      onClick={() => {
                                        const currentAssessments = form.getValues("riskAssessments") || [];
                                        const updatedAssessments = currentAssessments.filter((_, i) => i !== index);
                                        form.setValue("riskAssessments", updatedAssessments);
                                      }}
                                    >
                                      <X className="h-5 w-5" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Declaration Section */}
                        <div className="border-2 rounded-lg p-5 mb-8 bg-blue-50 border-blue-300 shadow-md">
                          <h3 className="text-xl font-bold mb-4 text-blue-800 flex items-center border-b border-blue-200 pb-3">
                            <Check className="mr-2 h-6 w-6 text-blue-600" />
                            Investigation Declaration
                          </h3>
                          <div className="bg-white p-4 rounded-md border border-blue-100">
                            <p className="text-base text-gray-800 leading-relaxed">
                              I confirm that the initial investigation and analysis of this incident have been completed, with corrective actions identified to address the root cause(s). This incident has been documented for further trend monitoring and analysis. Additional actions may be introduced by relevant stakeholders as needed to further strengthen controls and prevent recurrence.
                            </p>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    {/* Detailed Investigation & Analysis Tab - shown for comprehensive workflow stage */}
                    <TabsContent value="comprehensive-investigation" className="space-y-6">
                      <div className="form-section">
                        <div className={formSectionTitleClass}>
                          <div className={formSectionIconClass}>
                            <FileText size={20} />
                          </div>
                          <h2 className="text-xl font-semibold">Detailed Investigation & Analysis</h2>
                        </div>

                        {/* Use the ComprehensiveInvestigationForm component with isNested=true */}
                        <ComprehensiveInvestigationForm
                          incident={incident}
                          isNested={true}
                          onSubmit={(data) => {
                            // Update the form with the data from the ComprehensiveInvestigationForm
                            console.log("IncidentActionDialog - Detailed Investigation & Analysis onSubmit callback called with data:", data);
                            form.setValue('comprehensiveInvestigation', data, { shouldValidate: true });
                            console.log("IncidentActionDialog - After setting comprehensive investigation form value, form values:", form.getValues());
                          }}
                        />
                      </div>
                    </TabsContent>
                  </>
                )}

                {userRole === 'reviewer' && workflowStage !== 'investigation' && (
                  <TabsContent value="preview" className="space-y-6">
                    <div className="form-section">
                      {/* Review Banner */}
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 shadow-sm">
                        <div className="flex items-center gap-3">
                          <div className="bg-blue-100 p-2 rounded-full">
                            <FileText size={24} className="text-blue-600" />
                          </div>
                          <div>
                            <h2 className="text-xl font-bold text-blue-800">Final Review</h2>
                            <p className="text-blue-700">
                              Please carefully review all information before finalizing this incident and moving it to the investigation stage.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className={formSectionTitleClass}>
                        <div className={formSectionIconClass}>
                          <FileText size={20} />
                        </div>
                        <h2 className="text-xl font-semibold">Preview Incident Details</h2>
                      </div>

                      {/* Incident Details Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Check size={18} className="mr-2 text-green-600" />
                          Incident Details
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-4">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Title</p>
                            <p className="font-semibold text-gray-900">{form.getValues("incidentTitle")}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Date & Time</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("incidentDate") ? format(form.getValues("incidentDate"), "PPP") : "N/A"} at {form.getValues("incidentTime") || "N/A"}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Type</p>
                            <p className="font-semibold text-gray-900">{getIncidentTypeLabel(form.getValues("incidentType"))}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Incident Category</p>
                            <p className="font-semibold text-gray-900">{getIncidentCategoryLabel(form.getValues("incidentCategory"))}</p>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100 mb-4">
                          <p className="text-sm font-medium text-gray-500 mb-1">Description</p>
                          <p className="font-semibold text-gray-900">{form.getValues("description")}</p>
                        </div>
                        <div className="bg-white p-4 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                            <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Location Details
                          </p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            <div>
                              <p className="text-xs font-medium text-gray-500">Country</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.country")}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">City</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.city") || "N/A"}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Business Unit</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.businessUnit") || "N/A"}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Project/DC Ops</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.projectDcOps") || "N/A"}</p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Level and Location</p>
                              <p className="font-semibold text-gray-900">{form.getValues("location.levelAndLocation") || "N/A"}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Classification Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <AlertTriangle size={18} className="mr-2 text-amber-600" />
                          Classification
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Work Related</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("injuryClassification.isWorkRelated") === true ?
                                <span className="text-green-600">Yes</span> :
                                form.getValues("injuryClassification.isWorkRelated") === false ?
                                <span className="text-red-600">No</span> :
                                <span className="text-gray-500">Not Specified</span>}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Loss of Consciousness</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("injuryClassification.lossOfConsciousness") === true ?
                                <span className="text-red-600">Yes</span> :
                                form.getValues("injuryClassification.lossOfConsciousness") === false ?
                                <span className="text-green-600">No</span> :
                                <span className="text-gray-500">Not Specified</span>}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Dangerous Occurrence</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("injuryClassification.isDangerousOccurrence") === true ?
                                <span className="text-red-600">Yes</span> :
                                form.getValues("injuryClassification.isDangerousOccurrence") === false ?
                                <span className="text-green-600">No</span> :
                                <span className="text-gray-500">Not Specified</span>}
                            </p>
                          </div>
                        </div>

                        <div className="bg-white p-4 rounded-md border border-gray-100 mb-5">
                          <p className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                            <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                            Injury Classification
                          </p>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                            <div>
                              <p className="text-xs font-medium text-gray-500">Fatality</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isFatality") === true ?
                                  <span className="text-red-600">Yes</span> :
                                  form.getValues("injuryClassification.isFatality") === false ?
                                  <span className="text-green-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Permanent Disability</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isPermanentDisability") === true ?
                                  <span className="text-red-600">Yes</span> :
                                  form.getValues("injuryClassification.isPermanentDisability") === false ?
                                  <span className="text-green-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Lost Time Incident</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isLostTimeIncident") === true ?
                                  <span className="text-amber-600">Yes</span> :
                                  form.getValues("injuryClassification.isLostTimeIncident") === false ?
                                  <span className="text-green-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Medical Treatment</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isMedicalTreatment") === true ?
                                  <span className="text-amber-600">Yes</span> :
                                  form.getValues("injuryClassification.isMedicalTreatment") === false ?
                                  <span className="text-green-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">First Aid</p>
                              <p className="font-semibold">
                                {form.getValues("injuryClassification.isFirstAid") === true ?
                                  <span className="text-blue-600">Yes</span> :
                                  form.getValues("injuryClassification.isFirstAid") === false ?
                                  <span className="text-green-600">No</span> :
                                  <span className="text-gray-500">Not Specified</span>}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white p-3 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-1">Property Damage</p>
                          <p className="font-semibold">
                            {form.getValues("propertyDamage") ?
                              <span className="text-amber-600">Yes</span> :
                              <span className="text-green-600">No</span>}
                          </p>
                          {form.getValues("propertyDamage") && (
                            <div className="mt-2 pl-3 border-l-2 border-amber-200">
                              <p className="text-xs font-medium text-gray-500">Property Damage Details</p>
                              <p className="font-medium text-gray-900">{form.getValues("propertyDamageDetails") || "N/A"}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Conditions Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Info size={18} className="mr-2 text-blue-600" />
                          Environmental Conditions
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Surface Type</p>
                            <p className="font-semibold text-gray-900">{form.getValues("surfaceType") ? getSurfaceTypeLabel(form.getValues("surfaceType")) : "Not Specified"}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Surface Condition</p>
                            <p className="font-semibold text-gray-900">{form.getValues("surfaceCondition") ? getSurfaceConditionLabel(form.getValues("surfaceCondition")) : "Not Specified"}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Lighting</p>
                            <p className="font-semibold text-gray-900">{form.getValues("lighting") ? getLightingLabel(form.getValues("lighting")) : "Not Specified"}</p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Weather Condition</p>
                            <p className="font-semibold text-gray-900">{form.getValues("weatherCondition") ? getWeatherConditionLabel(form.getValues("weatherCondition")) : "Not Specified"}</p>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-1">Reportable to Authorities</p>
                          <p className="font-semibold">
                            {form.getValues("reportableToAuthorities") ?
                              <span className="text-amber-600">Yes</span> :
                              <span className="text-green-600">No</span>}
                          </p>
                          {form.getValues("reportableToAuthorities") && (
                            <div className="mt-2 pl-3 border-l-2 border-amber-200">
                              <p className="text-xs font-medium text-gray-500">Reporting Details</p>
                              <p className="font-medium text-gray-900">{form.getValues("reportableDetails") || "N/A"}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Actions Taken Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Check size={18} className="mr-2 text-green-600" />
                          Actions Taken
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Action Date</p>
                            <p className="font-semibold text-gray-900">
                              {form.getValues("immediateActionDate") ? format(form.getValues("immediateActionDate"), "PPP") : "N/A"}
                            </p>
                          </div>
                          <div className="bg-white p-3 rounded-md border border-gray-100">
                            <p className="text-sm font-medium text-gray-500 mb-1">Legal Classification</p>
                            <p className="font-semibold text-gray-900">{form.getValues("legalClassification") ? getLegalClassificationLabel(form.getValues("legalClassification")) : "Not Specified"}</p>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100 mb-4">
                          <p className="text-sm font-medium text-gray-500 mb-1">Actions Taken</p>
                          <p className="font-semibold text-gray-900">{form.getValues("immediateActionsTaken") || "N/A"}</p>
                        </div>

                      </div>

                      {/* Attachments Section */}
                      <div className="border border-gray-200 rounded-lg p-5 mb-8 bg-gray-50 shadow-sm">
                        <h3 className="text-xl font-bold mb-4 text-gray-800 flex items-center border-b pb-3">
                          <Upload size={18} className="mr-2 text-purple-600" />
                          Attachments
                        </h3>
                        <div className="bg-white p-3 rounded-md border border-gray-100">
                          <p className="text-sm font-medium text-gray-500 mb-1">Photos</p>
                          <p className="font-semibold text-gray-900 flex items-center">
                            {form.getValues("photos")?.length > 0 ? (
                              <>
                                <span className="inline-flex items-center justify-center w-6 h-6 mr-2 bg-purple-100 text-purple-700 rounded-full">
                                  {form.getValues("photos").length}
                                </span>
                                {`photo${form.getValues("photos").length > 1 ? 's' : ''} attached`}
                              </>
                            ) : (
                              <span className="text-gray-500">No photos attached</span>
                            )}
                          </p>
                        </div>
                      </div>

                      {/* Incident Classification Result */}
                      <div className="border-2 rounded-lg p-5 mb-8 bg-amber-50 border-amber-300 shadow-md">
                        <h3 className="text-xl font-bold mb-4 text-amber-800 flex items-center border-b border-amber-200 pb-3">
                          <AlertTriangle className="mr-2 h-6 w-6 text-amber-600" />
                          Final Incident Classification
                        </h3>
                        <div className="bg-white p-4 rounded-md border border-amber-100">
                          {form.getValues("injuryClassification.isFatality") === true && (
                            <p className="font-bold text-xl text-red-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-red-100 text-red-700 rounded-full font-bold">5</span>
                              Level 5 – Critical Incident
                            </p>
                          )}
                          {form.getValues("injuryClassification.isPermanentDisability") === true && form.getValues("injuryClassification.isFatality") !== true && (
                            <p className="font-bold text-xl text-orange-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-orange-100 text-orange-700 rounded-full font-bold">4</span>
                              Level 4 – High Severity Incident
                            </p>
                          )}
                          {form.getValues("injuryClassification.isLostTimeIncident") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && (
                            <p className="font-bold text-xl text-amber-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-amber-100 text-amber-700 rounded-full font-bold">3</span>
                              Level 3 – LTI- Medium Severity Incident
                            </p>
                          )}
                          {form.getValues("injuryClassification.isMedicalTreatment") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && (
                            <p className="font-bold text-xl text-yellow-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-yellow-100 text-yellow-700 rounded-full font-bold">2</span>
                              Level 2 – Low Severity Incident (MTI)
                            </p>
                          )}
                          {form.getValues("injuryClassification.isFirstAid") === true && form.getValues("injuryClassification.isFatality") !== true && form.getValues("injuryClassification.isPermanentDisability") !== true && form.getValues("injuryClassification.isLostTimeIncident") !== true && form.getValues("injuryClassification.isMedicalTreatment") !== true && (
                            <p className="font-bold text-xl text-blue-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-blue-100 text-blue-700 rounded-full font-bold">1</span>
                              Level 1 – Very Low Severity Incident (FAI)
                            </p>
                          )}
                          {form.getValues("injuryClassification.isFatality") === false && form.getValues("injuryClassification.isPermanentDisability") === false && form.getValues("injuryClassification.isLostTimeIncident") === false && form.getValues("injuryClassification.isMedicalTreatment") === false && form.getValues("injuryClassification.isFirstAid") === false && (
                            <p className="font-bold text-xl text-green-700 flex items-center">
                              <span className="inline-flex items-center justify-center w-8 h-8 mr-3 bg-green-100 text-green-700 rounded-full font-bold">0</span>
                              NEAR MISS
                            </p>
                          )}
                        </div>
                      </div>



                      {/* Declaration Section */}
                      <div className="border-2 rounded-lg p-5 mb-8 bg-blue-50 border-blue-300 shadow-md">
                        <h3 className="text-xl font-bold mb-4 text-blue-800 flex items-center border-b border-blue-200 pb-3">
                          <Check className="mr-2 h-6 w-6 text-blue-600" />
                          Declaration
                        </h3>
                        <div className="flex items-start space-x-4 p-4 bg-white rounded-md border border-blue-100">
                          <div className="flex-shrink-0 mt-0.5">
                            <Checkbox
                              id="declaration"
                              checked={declarationChecked}
                              onCheckedChange={(checked) => {
                                setDeclarationChecked(checked === true);
                              }}
                              className="h-5 w-5 border-2 border-blue-300"
                            />
                          </div>
                          <div>
                            <label
                              htmlFor="declaration"
                              className="text-base font-medium text-gray-800 leading-relaxed block"
                            >
                              I confirm that the above information is accurate to the best of my knowledge.
                            </label>
                            <p className="text-sm text-gray-600 mt-1">
                              Once submitted, the incident will be moved to the investigation stage. The details can be updated as needed during the investigation process.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                )}

                <div className="flex justify-between pt-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      // Define tabs based on workflow stage
                      let tabs: string[] = [];
                      if (workflowStage === 'comprehensive') {
                        tabs = ["comprehensive-investigation"];
                      } else if (workflowStage === 'investigation') {
                        tabs = ["investigation", "control-measures"];
                      } else if (userRole === 'reviewer') {
                        tabs = ["details", "classification", "conditions", "actions", "attachments", "preview"];
                      } else {
                        tabs = ["details", "classification", "conditions", "actions", "attachments"];
                      }

                      const currentTabIndex = tabs.indexOf(activeTab);
                      if (currentTabIndex > 0) {
                        setActiveTab(tabs[currentTabIndex - 1]);
                      }
                    }}
                    disabled={workflowStage === 'comprehensive' ?
                      activeTab === "comprehensive-investigation" :
                      workflowStage === 'investigation' ?
                      activeTab === "investigation" :
                      activeTab === "details"}
                    className="bg-white"
                  >
                    Previous
                  </Button>

                  {workflowStage === 'comprehensive' ? (
                    // Detailed Investigation & Analysis navigation logic (Step 4)
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={(e) => {
                          // Prevent default form submission
                          e.preventDefault();
                          saveChanges();
                        }}
                        className="bg-white"
                      >
                        Save
                      </Button>
                      <Button
                        type="submit"
                        onClick={(e) => {
                          // Submit the comprehensive investigation report
                          form.handleSubmit(onSubmit)(e);
                        }}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Submit Detailed Investigation & Analysis
                      </Button>
                    </div>
                  ) : workflowStage === 'investigation' ? (
                    // Investigation navigation logic (Step 3)
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={(e) => {
                          // Prevent default form submission
                          e.preventDefault();
                          saveChanges();
                        }}
                        className="bg-white"
                      >
                        Save
                      </Button>
                      {activeTab !== "control-measures" ? (
                        <Button
                          type="button"
                          onClick={(e) => {
                            // Prevent default form submission
                            e.preventDefault();

                            const tabs = ["investigation", "control-measures"];
                            const currentTabIndex = tabs.indexOf(activeTab);
                            if (currentTabIndex < 1) {
                              // Navigate to the next tab
                              setActiveTab(tabs[currentTabIndex + 1]);
                            }
                          }}
                          className="bg-primary hover:bg-primary/90"
                        >
                          Next
                        </Button>
                      ) : (
                        <Button
                          type="submit"
                          onClick={(e) => {
                            // Submit the investigation report for both reviewers and reporters
                            form.handleSubmit(onSubmit)(e);
                          }}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Submit Investigation
                        </Button>
                      )}
                    </div>
                  ) : userRole === 'reviewer' ? (
                    // Reviewer navigation logic with preview tab
                    <>
                      {activeTab !== "preview" ? (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              saveChanges();
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();

                              const tabs = ["details", "classification", "conditions", "actions", "attachments", "preview"];
                              const currentTabIndex = tabs.indexOf(activeTab);
                              if (currentTabIndex < 5) {
                                // Navigate to the next tab
                                setActiveTab(tabs[currentTabIndex + 1]);
                              }
                            }}
                            className="bg-primary hover:bg-primary/90"
                          >
                            {activeTab === "attachments" ? "Review & Finalize" : "Next"}
                          </Button>
                        </div>
                      ) : (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              saveChanges();
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="submit"
                            onClick={(e) => {
                              // Only submit the form when the button is clicked
                              form.handleSubmit(onSubmit)(e);
                            }}
                            className="bg-green-600 hover:bg-green-700"
                            disabled={!declarationChecked}
                          >
                            Submit
                          </Button>
                        </div>
                      )}
                    </>
                  ) : (
                    // Reporter navigation logic without preview tab
                    <>
                      {activeTab !== "attachments" ? (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              saveChanges();
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();

                              const tabs = ["details", "classification", "conditions", "actions", "attachments"];
                              const currentTabIndex = tabs.indexOf(activeTab);
                              if (currentTabIndex < 4) {
                                // Navigate to the next tab
                                setActiveTab(tabs[currentTabIndex + 1]);
                              }
                            }}
                            className="bg-primary hover:bg-primary/90"
                          >
                            Next
                          </Button>
                        </div>
                      ) : (
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              saveChanges();
                            }}
                            className="bg-white"
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            onClick={(e) => {
                              // Prevent default form submission
                              e.preventDefault();
                              saveChanges();
                              onOpenChange(false);
                            }}
                            className="bg-primary hover:bg-primary/90"
                          >
                            Save Changes
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </form>
            </Form>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IncidentActionDialog;
